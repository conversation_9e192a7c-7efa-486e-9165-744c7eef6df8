import { match } from 'ts-pattern';

import {
  type AnnouncementFlow,
  type BicycleEventType,
  type BicycleMarkerStatus,
  type BicycleType,
  type BodyField,
  type ErrorCode,
  type NotificationType,
  type PatrolFlow,
  type PoliceReferenceFileType,
  type ReferenceStatus,
  type ReleaseType,
  type ResponseStatus,
  type SearchField,
  type SearchItemType,
  objectKeys,
} from 'common';
import type {
  AnnouncementStatus,
  BicycleReferenceStatus,
  DeadlineStatus,
  MonthlyAnnouncementListStatus,
  NotificationStatus,
  ReleaseContractStatus,
} from 'lambda-api';
import type {
  BicycleContractStatus,
  Label,
  NumberPlateLocation,
  PostalData,
} from '../../../lambdas/.prisma';
import type { BicycleOwnerStatus, NoParkingAreaOption } from './bicycle';
import type { NavigationGroup } from './page';

/**
 * 固定のラベルを定義
 * システム全体で使用される変更されない基本的なラベル
 */
const constantLabels = {
  formalName: '氏名',
  policeStation: '警察署',
  postalCode: '郵便番号',
  prefecture: '都道府県',
  city: '市区町村',
  chome: '丁目',
  latlng: '緯度経度',

  theftReport: '盗難届',
  file: 'ファイル',
  searchSet: '検索セット',
  login: 'ログイン',
  other: 'その他',

  police: '警察',
  theft: '盗難',
  jaHolidays: '国民の祝日',
} as const;

/**
 * 一般的な業務に関するラベル
 */
const commonLabels = {
  inquiry: '問い合わせ',
  statistics: '統計',
} as const;

/**
 * 一般的な業務に関するラベル
 */
const businessLabels = {
  cost: '料金',
  price: '価格',
  contract: '契約',
  procedure: '手続き',
} as const;

/**
 * 時間・日付に関するラベル
 */
const timeLabels = {
  dateTime: '日時',
  date: '日',
  holiday: '祝日',
  pending: '未定',
  deadline: '期限',
  startOn: '開始日',
  endOn: '終了日',
} as const;

/**
 * 場所・住所に関するラベル
 */
const locationLabels = {
  address: '住所',
  town: '町名',
  townArea: '町域',
  furtherAddress: '丁目・番地・その他',
} as const;

/**
 * 連絡先に関するラベル
 */
const contactLabels = {
  email: 'メール',
  tel: '電話',
  fax: 'FAX',
} as const;

/**
 * 組織・チームに関するラベル
 */
const organizationLabels = {
  organization: '組織',
  department: '部署',
  section: '課',
  manager: '責任者',
  person: '担当者',
  team: 'チーム',
} as const;

/**
 * システム・UI に関するラベル
 */
const systemLabels = {
  home: 'ホーム',
  user: 'ユーザー',
  name: '名前',
  displayName: '表示名',
  kana: 'カナ',
  role: '権限',
  system: 'システム',
  status: 'ステータス',
  list: '一覧',
  details: '詳細',
  field: '項目',
  sortOrder: '表示順',
  label: 'ラベル',
  code: 'コード',
  memo: '備考',
  color: '色',
  colorCode: '色コード',
  search: '検索',
  fontSize: '文字サイズ',
  notification: '通知',
  image: '写真',
  description: '説明',
  settings: '設定',
  history: '履歴',
  qr: 'QRコード',
  csv: 'CSVファイル',
  xlsx: 'Excelファイル',
} as const;

/**
 * 一般的なアクション（動作）に関するラベル
 */
const actionLabels = {
  create: '登録',
  edit: '編集',
  modify: '修正',
  update: '更新',
  delete: '削除',
  cancel: 'キャンセル',
  append: '追加',
  remove: '削除',
  close: '閉じる',
  use: '使用',
  save: '保存',
  copy: 'コピー',
  reload: 'リロード',
  upload: 'アップロード',
  import: '取込',
  print: '印刷',
  refer: '照会',
  contact: '連絡',
  schedule: '予定',
  extend: '延長',
  collect: '受領',
  receive: '受付',
  start: '開始',
  done: '完了',
  completed: '済み',
  numbering: '採番',
  reNumbering: '再採番',
  setting: '設定',
  reset: 'リセット',
  manage: '管理',
  prepare: '準備',
  assigned: '担当者に設定され',
} as const;

/**
 * ドメイン固有の業務用語に関するラベル
 */
const domainLabels = {
  bicycle: '車両',
  map: 'マップ',
  marker: 'マーカー',
  location: '位置情報',
  landmark: '駅',
  abandoned: '放置',
  body: '車体',
  storage: '保管所',
  storageLocation: '保管場所',
  noParkingArea: '放置禁止区域',
  parking: '駐輪場',
  serialNumber: '整理番号',
  announcementSheet: '告示リスト',
  dltb: '陸運局',
  taxOffice: '税務課',
  storageNotice: '返還通知書',
  owner: '所有者',
  originalOwner: '持ち主',
  municipality: '自治体',
  recipient: '受取人',
  storageFee: '保管料',
  dealer: '取引先',
  dealType: '取引種別',
  releaseType: '処分種別',
  releaseTag: '処分タグ',
} as const;

/**
 * ドメイン固有のアクション（業務処理）に関するラベル
 */
const domainActionLabels = {
  patrol: '巡回',
  find: '発見',
  ensureAbandoned: '放置確認',
  remove: '撤去',
  lose: '消失',
  ignore: '無視',
  keep: '管理',
  store: '保管',
  notify: '通知',
  assess: '査定',
  announce: '告示',
  return: '返還',
  release: '処分',
  sell: '売却',
  transfer: '無償譲渡',
  dispose: '廃棄',
  request: '依頼',
  beforeRequest: '依頼前',
  acceptResponse: '回答受領',
  beforeResponse: '回答待ち',
  receive: '受領',
  move: '移動',
  recycle: 'リサイクル',
  loseFromStorage: '紛失',
} as const;

/**
 * 車体に関するラベル
 */
const bodyLabels = {
  type: '車両区分',
  color: '色',
  basket: 'カゴ',
  lock: '施錠',
  conditions: '車両状態',
  style: '車種',
  maker: 'メーカー',
  bicycleName: '車名',
  free1: '自由入力欄1',
  free2: '自由入力欄2',
  registrationNumber: '防犯登録番号',
  serialNumber: '車体番号',
  numberPlate: 'ナンバープレート',
} as const;

/**
 * 査定に関するラベル
 */
const assessmentLabels = {
  price: '価格',
} as const;

/**
 * 全てのラベルグループを統合したデフォルト辞書
 */
const defaultDict = {
  common: commonLabels,
  business: businessLabels,
  time: timeLabels,
  location: locationLabels,
  system: systemLabels,
  contact: contactLabels,
  org: organizationLabels,
  action: actionLabels,
  domain: domainLabels,
  domainAction: domainActionLabels,
  body: bodyLabels,
  assessment: assessmentLabels,
} as const;

/**
 * デフォルト辞書から生成されるラベル配列
 * データベース初期化などで使用される
 */
export const defaultLabelItems = Object.entries(defaultDict).flatMap(([group, labels]) =>
  Object.entries(labels).map(([key, value]) => ({ group, key, value })),
);

type DefaultDict = typeof defaultDict;

/**
 * 辞書グループの型
 */
export type DictGroup = keyof DefaultDict;

/**
 * 全ての辞書グループのキー配列
 */
export const dictGroups = objectKeys(defaultDict);

const labelGroupLabels = {
  common: 'その他一般名詞',
  business: 'ビジネス',
  time: '日付・時間',
  location: '場所',
  system: 'システム・UI',
  contact: '連絡先',
  org: '組織',
  action: '一般アクション',
  domain: '業務用語',
  domainAction: '業務アクション',
  body: '車体',
  assessment: '査定',
} as const satisfies Record<DictGroup, string>;

/**
 * ラベル辞書のエントリを作成する
 * データベースから取得したラベルデータか、デフォルトラベルのいずれかを使用してkey-valueのタプルを返す
 */
const entry = <T extends string>(
  items: Label[],
  group: DictGroup,
  key: T,
  defaultLabels: Record<T, string>,
): [T, string] => {
  const label =
    items.find((item) => item.group === group && item.key === key)?.value ?? defaultLabels[key];
  return [key, label];
};

/**
 * 指定されたグループの全ラベルエントリを作成するカリー化関数
 * デフォルトラベル定義から、データベースの値またはデフォルト値を使用したRecord型のオブジェクトを生成する
 */
const createEntries =
  (group: DictGroup, items: Label[]) =>
  <T extends string>(defaultLabels: Record<T, string>) =>
    Object.fromEntries(
      objectKeys(defaultLabels).map((field) => entry(items, group, field, defaultLabels)),
    ) as Record<T, string>;

/**
 * 指定されたグループのラベル辞書オブジェクトを取得する
 * パターンマッチングを使用してグループに対応するデフォルトラベルを適用する
 */
const getObjectByItems = (group: DictGroup, items: Label[]) => {
  const entries = createEntries(group, items);
  return match(group)
    .with('common', () => entries(commonLabels))
    .with('business', () => entries(businessLabels))
    .with('time', () => entries(timeLabels))
    .with('location', () => entries(locationLabels))
    .with('system', () => entries(systemLabels))
    .with('contact', () => entries(contactLabels))
    .with('org', () => entries(organizationLabels))
    .with('domain', () => entries(domainLabels))
    .with('action', () => entries(actionLabels))
    .with('domainAction', () => entries(domainActionLabels))
    .with('body', () => entries(bodyLabels))
    .with('assessment', () => entries(assessmentLabels))
    .exhaustive();
};

/**
 * ラベルの配列を受け取り、グループごとのラベル辞書を作成する
 *
 * ※API側で使用する
 */
export const getLabelDict = (items: Label[]) => {
  const entries = dictGroups.map((group) => [group, getObjectByItems(group, items)]);
  return Object.fromEntries(entries) as DefaultDict;
};

/**
 * 文字列がDictGroupかどうかを判定する型ガード関数
 */
export const isDictGroup = (group: string): group is DictGroup => dictGroups.includes(group);

/**
 * 指定されたラベルのデフォルト値を取得する
 * グループまたはキーが見つからない場合はエラーを投げる
 */
export const getUncheckedDefaultLabelValue = (group: string, key: string): string | undefined => {
  if (!isDictGroup(group)) return undefined;
  const defaultLabels = defaultDict[group];
  return Object.entries(defaultLabels)
    .find(([k]) => k === key)
    ?.at(1);
};

export const getDefaultLabelValue = <G extends DictGroup, K extends keyof DefaultDict[G]>(
  group: G,
  key: K,
): string => {
  const label = getUncheckedDefaultLabelValue(group, key as string);
  if (!label) throw new Error(`Label not found for group: ${group}, key: ${key as string}`);
  return label;
};

export const defLabel = <G extends DictGroup, K extends keyof DefaultDict[G]>(
  group: G,
  key: K,
  value: string,
) => ({ group, key, value });

type DefaultDictWithConstant = DefaultDict & {
  constant: typeof constantLabels;
};

/**
 * よく使用される複合ラベル（フレーズ）の辞書を作成する
 */
const createPhrase = (dict: DefaultDictWithConstant) =>
  ({
    serialTag: `${dict.domain.serialNumber}札`,
    announcementStatus: `${dict.domainAction.announce}${dict.system.status}`,
    storeDeadline: `${dict.domainAction.store}${dict.time.deadline}`,
    referPolice: `${constantLabels.police}${dict.action.refer}`,
    referTaxOffice: `${dict.domain.taxOffice}${dict.action.refer}`,
    referDltb: `${dict.domain.dltb}${dict.action.refer}`,
    numberingSerialNumber: `${dict.domain.serialNumber}${dict.action.numbering}`,
    notifyReturn: `${dict.domainAction.return}${dict.domainAction.notify}`,
    createdAt: `${dict.action.create}${dict.time.dateTime}`,
    updatedAt: `${dict.action.update}${dict.time.dateTime}`,
    removedAt: `${dict.domainAction.remove}${dict.time.dateTime}`,
    notificationStatus: `${dict.domainAction.notify}${dict.system.status}`,
    notificationDate: `${dict.domainAction.notify}${dict.time.date}`,
    announcementStart: `${dict.domainAction.announce}${dict.time.startOn}`,
    announcementEnd: `${dict.domainAction.announce}${dict.time.endOn}`,
    deadlineStatus: `${dict.domainAction.store}${dict.time.deadline}${dict.system.status}`,
    deadlineDate: `${dict.domainAction.store}${dict.time.deadline}${dict.time.date}`,
    ownerInfo: `${dict.domain.owner}情報`,
    ownerStatus: `${dict.domain.owner}${dict.system.status}`,
    ownerName: `${dict.domain.owner}名`,
    ownerPostalCode: `${dict.domain.owner}${constantLabels.postalCode}`,
    ownerAddress: `${dict.domain.owner}${dict.location.address}`,
    referenceStatus: `${dict.action.refer}${dict.system.status}`,
    releaseStatus: `${dict.domainAction.release}${dict.system.status}`,
    releaseContract: `${dict.domainAction.release}${dict.business.contract}`,
    releaseDate: `${dict.domainAction.release}${dict.time.dateTime}`,
    isCollectedStorageFee: `${dict.domain.storageFee}${dict.action.collect}${dict.system.status}`,
    numberingAt: `${dict.domain.serialNumber}${dict.action.numbering}${dict.time.dateTime}`,
    bodyInfo: `${dict.domain.body}情報`,
    hasBasket: `${dict.body.basket}の有無`,
    hasBasketTrue: `${dict.body.basket}あり`,
    hasBasketFalse: `${dict.body.basket}なし`,
    hasBasketNull: `${dict.body.basket}データなし`,
    isLocked: `${dict.body.lock}の有無`,
    isLockedTrue: `${dict.body.lock}済み`,
    isLockedFalse: `${dict.body.lock}なし`,
    isLockedNull: `${dict.body.lock}データなし`,
    isNoParkingAreaTrue: `${dict.domain.noParkingArea}内`,
    isNoParkingAreaFalse: `${dict.domain.noParkingArea}外`,
    isNoParkingAreaNull: `${dict.domain.noParkingArea}データなし`,
    contractStatus: `${dict.business.contract}${dict.system.status}`,
    contractCannotCancel: `${dict.business.contract}は${dict.action.cancel}後、元に戻すことはできません。
    本当に${dict.business.contract}を${dict.action.cancel}しますか？`,
    sellContract: `${dict.domainAction.sell}${dict.business.contract}`,
    dealerName: `${dict.domain.dealer}名`,
    numberPlateLocation: `${dict.body.numberPlate}表示（地域名）`,
    defaultPrice: `${dict.assessment.price}初期値`,
    csvTemplateFileName: 'マスタテンプレート.csv',
    successUploadFile: `${dict.constant.file}の${dict.action.upload}が完了しました`,
    autoFillAddressAndLatLng: `${dict.location.address}と${dict.constant.latlng}を自動入力`,
    convertAddressToLatLng: `${dict.location.address}を${dict.constant.latlng}に変換`,
    convertLatLngToAddress: `${dict.constant.latlng}を${dict.location.address}に変換`,
    canLogin: `${dict.constant.login}可否`,
    canLoginTrue: `${dict.constant.login}可能`,
    alreadyUsed: `既に${dict.action.use}されています`,
    alreadyCreated: `既に${dict.action.create}されています`,
  }) as const;

type CommonDict = DefaultDict & {
  /** phrase */
  p: ReturnType<typeof createPhrase>;
  constant: typeof constantLabels;
};

/**
 * オブジェクトの部分的なラベル型
 */
type Labels<T extends object> = Partial<Record<keyof T, string>>;

/**
 * 車両タイプのラベル辞書を作成する
 */
const createBicycleType = (dict: CommonDict) =>
  ({
    bicycle: '自転車',
    motorizedBicycle: '原付',
    motorcycle: '二輪車',
    specifiedSmallMotorizedBicycle: '特定小型原動機付自転車',
    other: dict.constant.other,
  }) as const satisfies Record<BicycleType, string>;

/**
 * 車体フィールドのラベル辞書を作成する
 */
const createBodyField = (dict: CommonDict) =>
  ({
    colors: dict.body.color,
    hasBasket: dict.body.basket,
    isLocked: `${dict.body.lock}有無`,
    conditions: dict.body.conditions,
    style: dict.body.style,
    registrationNumber: dict.body.registrationNumber,
    serialNumber: dict.body.serialNumber,
    numberPlate: dict.body.numberPlate,
    free1: dict.body.free1,
    free2: dict.body.free2,
  }) as const satisfies Record<BodyField, string>;

const createPatrolFlow = (dict: CommonDict) =>
  ({
    find: `${dict.domainAction.patrol}で${dict.domainAction.find}した${dict.domain.bicycle}はQRコードを使用して${dict.action.manage}する`,
    mark: `${dict.domainAction.patrol}では${dict.domain.map}上に表示する${dict.domain.marker}を${dict.action.create}する`,
    noPatrol: `即時${dict.action.remove}`,
  }) as const satisfies Record<PatrolFlow, string>;

const createAnnouncementFlow = (dict: CommonDict) =>
  ({
    monthly: `${dict.domain.announcementSheet}により月ごとに${dict.domainAction.announce}${dict.business.procedure}する`,
    each: `${dict.domain.bicycle}それぞれに${dict.domainAction.announce}${dict.business.procedure}する`,
  }) as const satisfies Record<AnnouncementFlow, string>;

/**
 * 自転車イベントタイプのラベル辞書を作成する
 */
const createEventType = (dict: CommonDict) =>
  ({
    find: dict.domainAction.find,
    ensureAbandoned: dict.domainAction.ensureAbandoned,
    remove: dict.domainAction.remove,
    lose: dict.domainAction.lose,
    ignore: dict.domainAction.ignore,
    numbering: dict.p.numberingSerialNumber,
    reNumbering: `${dict.domain.serialNumber}の${dict.action.reNumbering}`,
    printSerialTag: `${dict.p.serialTag}の${dict.action.print}`,
    store: dict.domainAction.store,
    copyOwnerInfo: `${dict.domain.owner}情報${dict.action.copy}`,
    appendAnnouncementList: `${dict.domain.announcementSheet}${dict.action.append}`,
    removeAnnouncementList: `${dict.domain.announcementSheet}${dict.action.remove}`,
    startAnnouncement: `${dict.domainAction.announce}${dict.action.start}`,
    updateAnnouncement: `${dict.domainAction.announce}${dict.action.update}`,
    cancelAnnouncement: `${dict.domainAction.announce}${dict.action.cancel}`,
    requestPolice: dict.p.referPolice,
    receivePolice: `${dict.p.referPolice}${dict.action.collect}`,
    cancelPolice: `${dict.p.referPolice}${dict.action.cancel}`,
    requestDltb: dict.p.referDltb,
    receiveDltb: `${dict.p.referDltb}${dict.action.collect}`,
    cancelDltb: `${dict.p.referDltb}${dict.action.cancel}`,
    notify: dict.p.notifyReturn,
    updateNotification: `${dict.domainAction.notify}日${dict.action.update}`,
    cancelNotification: `${dict.domainAction.notify}日${dict.action.cancel}`,
    resetNotificationDate: `${dict.domainAction.notify}日${dict.action.reset}`,
    extendDeadline: `${dict.p.storeDeadline}の${dict.action.extend}`,
    scheduleDeadline: `${dict.p.storeDeadline}の${dict.action.schedule}`,
    updateDeadline: `${dict.p.storeDeadline}の${dict.action.update}`,
    cancelDeadline: `${dict.p.storeDeadline}の${dict.action.cancel}`,
    scheduleMoveBetweenStorage: `${dict.domain.storage}から${dict.domain.storage}への${dict.domainAction.move}${dict.action.schedule}`,
    moveBetweenStorage: `${dict.domain.storage}から${dict.domain.storage}への${dict.domainAction.move}`,
    returnToOwner: dict.domainAction.return,
    cancelReturnToOwner: `${dict.domainAction.return}${dict.action.cancel}`,
    collectStorageFee: `${dict.domain.storageFee}${dict.action.collect}`,
    cancelCollectStorageFee: `${dict.domain.storageFee}${dict.action.collect}${dict.action.cancel}`,
    sell: dict.domainAction.sell,
    cancelSell: `${dict.domainAction.sell}${dict.action.cancel}`,
    scheduleSell: `${dict.domainAction.sell}${dict.action.schedule}`,
    editScheduleSell: `${dict.domainAction.sell}${dict.action.schedule}${dict.action.edit}`,
    cancelSellSchedule: `${dict.domainAction.sell}${dict.action.schedule}${dict.action.cancel}`,
    transfer: dict.domainAction.transfer,
    cancelTransfer: `${dict.domainAction.transfer}${dict.action.cancel}`,
    scheduleTransfer: `${dict.domainAction.transfer}${dict.action.schedule}`,
    cancelTransferSchedule: `${dict.domainAction.transfer}${dict.action.schedule}${dict.action.cancel}`,
    dispose: dict.domainAction.dispose,
    cancelDispose: `${dict.domainAction.dispose}${dict.action.cancel}`,
    scheduleDispose: `${dict.domainAction.dispose}${dict.action.schedule}`,
    cancelDisposeSchedule: `${dict.domainAction.dispose}${dict.action.schedule}${dict.action.cancel}`,
    recycle: dict.domainAction.recycle,
    cancelRecycle: `${dict.domainAction.recycle}${dict.action.cancel}`,
    scheduleRecycle: `${dict.domainAction.recycle}${dict.action.schedule}`,
    cancelRecycleSchedule: `${dict.domainAction.recycle}${dict.action.schedule}${dict.action.cancel}`,
    loseFromStorage: dict.domainAction.loseFromStorage,
    cancelLoseFromStorage: `${dict.domainAction.loseFromStorage}${dict.action.cancel}`,
    create: `${dict.domain.bicycle}${dict.action.create}`,
    editLocation: `${dict.domain.location}の${dict.action.edit}`,
    editBody: `${dict.p.bodyInfo}の${dict.action.edit}`,
    editOwner: `${dict.p.ownerInfo}の${dict.action.edit}`,
    assess: dict.domainAction.assess,
  }) as const satisfies Record<BicycleEventType, string>;

/**
 * 処分タイプのラベル辞書を作成する
 */
const createReleaseType = (dict: CommonDict) =>
  ({
    sell: dict.domainAction.sell,
    transfer: dict.domainAction.transfer,
    dispose: dict.domainAction.dispose,
    recycle: dict.domainAction.recycle,
  }) as const satisfies Record<ReleaseType, string>;

/**
 * 放置禁止区域の有無のラベル辞書を作成する
 */
const createIsNoParkingArea = (dict: CommonDict) =>
  ({
    isNoParkingAreaTrue: `${dict.domain.noParkingArea}`,
    isNoParkingAreaFalse: `${dict.domain.noParkingArea}外`,
  }) as const satisfies Record<NoParkingAreaOption, string>;

/**
 * ナンバープレート位置情報のラベル辞書を作成する
 */
const createNumberPlateLocation = (dict: CommonDict) =>
  ({
    name: '地域名', // 定数適用は不要
    prefecture: constantLabels.prefecture,
    dltb: dict.domain.dltb,
    dltbBranch: '運輸支局・自動車検査登録事務所',
    areas: '管轄区域', // 定数適用は不要
  }) as const satisfies Labels<NumberPlateLocation>;

/**
 * 通知ステータスのラベル辞書を作成する
 */
const createNotificationStatus = (dict: CommonDict) =>
  ({
    todo: dict.time.pending,
    done: `${dict.domainAction.notify}${dict.action.completed}`,
  }) as const satisfies Record<NotificationStatus, string>;

const createPoliceReferenceFileType = (dict: CommonDict) =>
  ({
    any: 'いずれか',
    csv: dict.system.csv,
    xlsx: dict.system.xlsx,
  }) as const satisfies Record<PoliceReferenceFileType, string>;

/**
 * 郵便番号データのラベル辞書を作成する
 */
const createPostalCode = (dict: CommonDict) =>
  ({
    code: constantLabels.postalCode,
    index: dict.system.sortOrder,
    prefecture: constantLabels.prefecture,
    prefectureKana: `${constantLabels.prefecture}（${dict.system.kana}）`,
    city: constantLabels.city,
    cityKana: `${constantLabels.city}（${dict.system.kana}）`,
    townArea: dict.location.townArea,
    townAreaRaw: `${dict.location.townArea}(${constantLabels.chome}の形式を含む)`,
    townAreaKana: `${dict.location.townArea}（${dict.system.kana}）`,
    hasChome: `${constantLabels.chome}の有無`,
    chomeList: `${constantLabels.chome}${dict.system.list}`,
    // 以下、定数適用は不要
    nationalLocalPublicEntityCode: '全国地方公共団体コード',
    oldPostalCode: '旧郵便番号',
    hasAnotherPostalCode: '別の郵便番号の有無',
    isNumberedForEachKoaza: '小字ごとに番地が設定されているか',
    hasMultipleTownArea: '1つの郵便番号で複数の町域を表すか',
  }) as const satisfies Labels<PostalData>;

/**
 * マーカーステータスのラベル辞書を作成する
 */
const createMarkerStatusLabels = (dict: CommonDict) =>
  ({
    todo: dict.domainAction.find,
    done: dict.action.done,
  }) as const satisfies Record<BicycleMarkerStatus, string>;

/**
 * 告示ステータスのラベル辞書を作成する
 */
const createAnnouncementStatus = (dict: CommonDict) =>
  ({
    pending: dict.time.pending,
    scheduled: `${dict.domainAction.announce}予定`,
    active: `${dict.domainAction.announce}中`,
    closed: `${dict.domainAction.announce}${dict.action.done}`,
  }) as const satisfies Record<AnnouncementStatus, string>;

/**
 * 期限ステータスのラベル辞書を作成する
 */
const createDeadlineStatus = (dict: CommonDict) => {
  const base = `${dict.domainAction.store}${dict.time.deadline}` as const;
  return {
    pending: `${base}${dict.time.pending}`,
    scheduled: `${base}あり`,
    releasable: `${base}満了`,
  } as const satisfies Record<DeadlineStatus, string>;
};

/**
 * 所有者ステータスのラベル辞書を作成する
 */
const createOwnerStatus = (dict: CommonDict) =>
  ({
    original: dict.domain.originalOwner,
    municipality: dict.domain.municipality,
  }) as const satisfies Record<BicycleOwnerStatus, string>;

/**
 * 照会ステータスのラベル辞書を作成する
 */
const createBicycleReferenceStatus = (dict: CommonDict) =>
  ({
    notStored: '保管前',
    notRequested: `${dict.action.refer}${dict.domainAction.beforeRequest}`,
    requested: `${dict.action.refer}${dict.domainAction.request}済み`,
    responded: `${dict.action.refer}${dict.domainAction.acceptResponse}済み`,
  }) as const satisfies Record<BicycleReferenceStatus, string>;

/**
 * 照会ステータスのラベル辞書を作成する
 */
const createReferenceStatus = (dict: CommonDict) =>
  ({
    request: `${dict.action.refer}${dict.domainAction.request}済み`,
    response: `${dict.action.refer}${dict.domainAction.acceptResponse}済み`,
    canceled: `${dict.action.refer}${dict.action.cancel}済み`,
  }) as const satisfies Record<ReferenceStatus, string>;

const createResponseStatus = (dict: CommonDict) =>
  ({
    success: `${dict.domainAction.acceptResponse}済み`,
    notFound: `${dict.action.refer}結果なし`,
  }) as const satisfies Record<ResponseStatus, string>;

/**
 * 照会ステータスのラベル辞書を作成する
 */
const createMonthlyAnnouncementListStatus = (dict: CommonDict) =>
  ({
    waiting: `${dict.domainAction.announce}${dict.action.prepare}中`,
    doing: `${dict.domainAction.announce}中`,
    done: `${dict.domainAction.announce}${dict.action.cancel}済み`,
  }) as const satisfies Record<MonthlyAnnouncementListStatus, string>;

/**
 * 処分契約ステータスのラベル辞書を作成する
 */
const createReleaseContractStatus = (dict: CommonDict) =>
  ({
    pending: `${dict.domainAction.release}${dict.business.contract}${dict.time.pending}` as const,
    sellDone: `${dict.domainAction.sell}${dict.business.contract}${dict.action.completed}` as const,
    sellScheduled:
      `${dict.domainAction.sell}${dict.business.contract}${dict.action.schedule}` as const,
    transferDone:
      `${dict.domainAction.transfer}${dict.business.contract}${dict.action.completed}` as const,
    transferScheduled:
      `${dict.domainAction.transfer}${dict.business.contract}${dict.action.schedule}` as const,
    disposeDone:
      `${dict.domainAction.dispose}${dict.business.contract}${dict.action.completed}` as const,
    disposeScheduled:
      `${dict.domainAction.dispose}${dict.business.contract}${dict.action.schedule}` as const,
  }) as const satisfies Record<ReleaseContractStatus, string>;

/**
 * 通知タイプのラベル辞書を作成する
 */
const createNotificationType = (dict: CommonDict) =>
  ({
    inquiryAssignment: `${dict.common.inquiry}割り当て`,
    system: dict.system.system,
  }) as const satisfies Record<NotificationType, string>;

/**
 * ナビゲーションのラベル辞書を作成する
 */
const createNavigation = (dict: CommonDict) =>
  ({
    home: dict.system.home,
    patrol: dict.domainAction.patrol,
    keep: `${dict.domain.bicycle}${dict.domainAction.keep}`,
    release: dict.domainAction.release,
    statistics: dict.common.statistics,
    settings: dict.system.settings,
    system: 'システム管理',
  }) as const satisfies Record<NavigationGroup, string>;

/**
 * 検索フィールドのラベル辞書を作成する
 */
const createSearchField = (dict: CommonDict) =>
  ({
    createdAt: dict.p.createdAt,
    removedAt: dict.p.removedAt,

    status: dict.system.status,

    storage: dict.domain.storage,
    storageLocationMemo: `${dict.domain.storageLocation}${dict.system.memo}`,
    serialNo: dict.domain.serialNumber,

    bicycleType: dict.body.type,
    registrationNumber: dict.body.registrationNumber,
    serialNumber: dict.body.serialNumber,
    numberPlate: dict.body.numberPlate,
    colors: dict.body.color,
    hasBasket: dict.p.hasBasket,
    isLocked: dict.p.isLocked,
    conditions: dict.body.conditions,

    landmark: dict.domain.landmark,
    isNoParkingArea: dict.domain.noParkingArea,
    address: `${dict.domain.abandoned}場所`,

    isRemovedOrStored: `${dict.domainAction.remove}または${dict.domainAction.store}`,
    referenceStatus: `${dict.action.refer}${dict.system.status}`,
    ownerName: `${dict.domain.owner}名`,
    ownerPostalCode: `${dict.domain.owner}${constantLabels.postalCode}`,
    ownerAddress: `${dict.domain.owner}${dict.location.address}`,
    theftReport: dict.constant.theftReport,
    announceStatus: `${dict.domainAction.announce}${dict.system.status}`,
    isReturned: `${dict.domainAction.return}${dict.action.completed}`,
    isCollectedStorageFee: `${dict.domain.storageFee}${dict.action.collect}${dict.action.completed}`,
    isReleased: `${dict.domainAction.release}${dict.action.completed}`,
    releaseStatus: `${dict.domainAction.release}${dict.system.status}`,
    ownerStatus: `${dict.domain.owner}${dict.system.status}`,
  }) as const satisfies Record<SearchField, string>;

/**
 * 検索アイテムタイプのラベル辞書
 * 変更したいケースはかなり少ないと予想されるため定数として定義する
 */
const searchItemTypeLabels = {
  text: '部分一致',
  switch: 'スイッチ',
  selectSingle: '単一選択',
  selectMultiple: '複数選択',
  date: '日付',
  dateAfter: '以降',
  dateBefore: '以前',
} as const satisfies Record<SearchItemType, string>;

const createContractStatus = (dict: CommonDict) =>
  ({
    scheduled: `${dict.business.contract}${dict.action.schedule}`,
    done: `${dict.business.contract}${dict.action.done}`,
    canceled: `${dict.business.contract}${dict.action.cancel}`,
  }) as const satisfies Record<BicycleContractStatus, string>;

const createError = (dict: CommonDict) =>
  ({
    EMAIL_ALREADY_EXISTS: `この${dict.contact.email}は${dict.p.alreadyUsed}`,
    DISPLAY_NAME_ALREADY_EXISTS: `この${dict.system.displayName}は${dict.p.alreadyUsed}`,
    DEALER_NAME_ALREADY_EXISTS: `この${dict.domain.dealer}名は${dict.p.alreadyUsed}`,
    POLICE_STATION_NAME_ALREADY_EXISTS: `この${dict.constant.policeStation}は${dict.p.alreadyUsed}`,
    POSTAL_CODE_ALREADY_EXISTS: `この${dict.constant.postalCode}は${dict.p.alreadyUsed}`,
    PARKING_CODE_ALREADY_EXISTS: `この${dict.domain.parking}${dict.system.code}は${dict.p.alreadyUsed}`,
    MAKER_NAME_ALREADY_EXISTS: `この${dict.body.maker}名は${dict.p.alreadyUsed}`,
    RELEASE_TAG_CODE_ALREADY_EXISTS: `この${dict.domain.releaseTag}${dict.system.code}は${dict.p.alreadyUsed}`,
    MOTORIZED_BICYCLE_NUMBER_PLATE_CONTACT_ALREADY_CREATED: `この地域名は${dict.p.alreadyCreated}`,
    DLTB_CONTACT_ALREADY_CREATED: `この${dict.p.referDltb}先は${dict.p.alreadyCreated}`,
    BICYCLE_NAME_CODE_ALREADY_EXISTS: `この${dict.domain.bicycle}${dict.system.code}は${dict.p.alreadyUsed}`,
    COLOR_NAME_ALREADY_EXISTS: `この${dict.body.color}名は${dict.p.alreadyUsed}`,
  }) as const satisfies Record<ErrorCode, string>;

/**
 * ラベルに「する」を付けた動詞形の型定義
 */
type Doing<T extends Record<string, string>> = {
  [K in keyof T]: `${T[K]}する`;
};

/**
 * ラベルに「する」を付けた動詞形のラベル辞書を作成する
 */
const createDoing = <T extends Record<string, string>>(obj: T): Doing<T> => {
  const result = {} as Doing<T>;
  for (const key in obj) {
    result[key] = `${obj[key]}する` as Doing<T>[typeof key];
  }
  return result;
};

/**
 * フロント側で使用するラベルオブジェクトを作成する
 * 全ての種類のラベル辞書を統合し、UI で使用しやすい形に整理する
 *
 * ※フロント側で使用する
 */
export const getDict = (dict: DefaultDict) => {
  const phrase = createPhrase({ ...dict, constant: constantLabels });
  const dictWithPhrases: CommonDict = { ...dict, p: phrase, constant: constantLabels };
  const doing = createDoing(dict.action);
  const domainDoing = createDoing(dict.domainAction);
  const eventType = createEventType(dictWithPhrases);
  const eventDoing = createDoing(eventType);

  return {
    ...dictWithPhrases,
    labelGroup: labelGroupLabels,
    doing,
    /** domainAction */
    domainAction: undefined,
    da: dict.domainAction,
    /** domainDoing */
    dd: domainDoing,
    /** eventType  */
    eventType: undefined,
    et: eventType,
    /** eventDoing  */
    ed: eventDoing,
    location: {
      ...dict.location,
      postalCode: constantLabels.postalCode,
    },
    patrolFlow: createPatrolFlow(dictWithPhrases),
    announcementFlow: createAnnouncementFlow(dictWithPhrases),

    isNoParkingArea: createIsNoParkingArea(dictWithPhrases),
    numberPlateLocation: createNumberPlateLocation(dictWithPhrases),
    postalCode: createPostalCode(dictWithPhrases),
    markerStatus: createMarkerStatusLabels(dictWithPhrases),
    bicycleType: createBicycleType(dictWithPhrases),
    bodyField: createBodyField(dictWithPhrases),
    notificationStatus: createNotificationStatus(dictWithPhrases),
    policeReferenceFileType: createPoliceReferenceFileType(dictWithPhrases),
    announcementStatus: createAnnouncementStatus(dictWithPhrases),
    deadlineStatus: createDeadlineStatus(dictWithPhrases),
    ownerStatus: createOwnerStatus(dictWithPhrases),
    referenceStatus: createReferenceStatus(dictWithPhrases),
    responseStatus: createResponseStatus(dictWithPhrases),
    bicycleReferenceStatus: createBicycleReferenceStatus(dictWithPhrases),
    monthlyAnnouncementListStatus: createMonthlyAnnouncementListStatus(dictWithPhrases),
    releaseType: createReleaseType(dictWithPhrases),
    releaseContractStatus: createReleaseContractStatus(dictWithPhrases),
    notificationType: createNotificationType(dictWithPhrases),
    navigation: createNavigation(dictWithPhrases),
    searchField: createSearchField(dictWithPhrases),
    searchItemType: searchItemTypeLabels,
    contractStatus: createContractStatus(dictWithPhrases),
    error: createError(dictWithPhrases),
  } as const;
};

/**
 * getLabelObject関数の戻り値の型
 */
export type Dict = ReturnType<typeof getDict>;

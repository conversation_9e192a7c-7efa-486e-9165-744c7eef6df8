import { Box, Paper } from '@mui/material';
import type { GridColDef, GridRowParams } from '@mui/x-data-grid';
import { useNavigate } from '@tanstack/react-router';

import type { Inquiries } from 'lambda-api';
import { DataGrid } from 'mui-ex';

import { trpc } from '@/api';
import { createImageColProps } from '@/components/ImageAvatar';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormat, datetimeFormatter } from '@/funcs/date';
import { useTextToPx } from '@/hooks/useTextToPx';
import { DataGridToolbar } from '@/libs/mui-x/grid/DataGridToolbar';
import { useGridExport } from '@/libs/mui-x/grid/utils';
import { inquiryRouters } from '@/router/routes/inquiry';
import { BASE_URL } from '@/types/vite-env';
import { getQueryKey } from '@trpc/react-query';

type Inquiry = Inquiries[number];

const useColumns = (): GridColDef<Inquiry>[] => {
  const fit = useTextToPx();
  const { labels } = useAppContext();
  return [
    {
      field: 'images',
      ...createImageColProps<Inquiry>((inquiry) => {
        const key = inquiry.images?.[0]?.key;
        if (!key) return undefined;
        return `${BASE_URL}/${key}`;
      }),
    },
    /** 日時 */
    {
      field: 'createdAt',
      headerName: '受付日時',
      width: fit(datetimeFormat),
      valueGetter: (_, row) => datetimeFormatter(row.createdAt),
    },

    /** 住所 */
    {
      field: 'address',
      headerName: '住所',
      width: 300,
    },

    {
      field: 'description',
      headerName: '問い合わせ内容',
      width: 400,
      valueGetter: (_, row) => row.description || 'No Description',
    },
    {
      field: 'assignUsers',
      headerName: labels.org.person,
      width: 100,
      valueGetter: (_, row) => row.assignUsers?.[0]?.user?.displayName,
    },
  ];
};

const useInquiryExport = () => {
  const cols = useColumns();
  return useGridExport(cols, inquiryRouters.meta.list.useTitle());
};

const Toolbar = () => {
  const exportFormatter = useInquiryExport();
  return (
    <DataGridToolbar
      to="/inquiries/create"
      queryKey={getQueryKey(trpc.inquiries.list)}
      exportFormatter={exportFormatter}
    />
  );
};

export default function InquiryListPage() {
  const columns = useColumns();

  const { data: inquiry = [], isPending } = trpc.inquiries.list.useQuery();
  const navigate = useNavigate();

  const handleRowClick = (params: GridRowParams<Inquiry>) =>
    navigate({ to: '/inquiries/$id', params: { id: params.id.toString() } });

  return (
    <Box sx={{ p: 2, height: 1 }}>
      <Paper sx={{ height: 1 }}>
        <DataGrid
          persistent="/inquiries"
          columns={columns}
          rows={inquiry}
          onRowClick={handleRowClick}
          slots={{ toolbar: Toolbar }}
          loading={isPending}
          sx={{
            // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
            '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
            '& .MuiTablePagination-select': { mr: 2 },
          }}
        />
      </Paper>
    </Box>
  );
}
